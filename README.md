# Roylia - The Social Network for the Mentally Royal

A modern social networking application built with Meteor.js, React, and MongoDB.

## 🚀 Features

- **Beautiful Modern UI** with light/dark theme switching
- **User Authentication** (Registration & Login)
- **Remote MongoDB Integration** 
- **Responsive Design** that works on all devices
- **Custom Date Input** with automatic formatting (dd.mm.yyyy)
- **Real-time Updates** powered by Meteor

## 🛠️ Tech Stack

- **Frontend**: React 18.2.0 with JSX
- **Backend**: Meteor.js 3.3
- **Database**: MongoDB (Remote)
- **Styling**: Custom CSS with CSS Variables
- **Authentication**: Meteor Accounts (Password-based)
- **Font**: Inter (Google Fonts)

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v14 or higher)
- **npm** (comes with Node.js)
- **Meteor.js** (v3.3 or higher)

### Installing Meteor

```bash
# On macOS/Linux
curl https://install.meteor.com/ | sh

# On Windows
# Download and run the installer from https://www.meteor.com/install
```

## 🔧 Installation & Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd royalia
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Configuration

The application is pre-configured to use a remote MongoDB database. The connection details are embedded in the npm scripts for easy setup.

**No additional environment configuration needed!**

### 4. Start the Application

For development (recommended):
```bash
npm run dev
```

For standard start:
```bash
npm start
```

The application will be available at: **http://localhost:3001**

## 🎨 UI Features

### Theme Switching
- Click the 🌙/☀️ button in the top-right corner to switch between light and dark themes
- Themes are automatically applied with smooth transitions

### Custom Date Input
- Date of birth field uses a custom format: **dd.mm.yyyy**
- Automatic dot insertion after day and month
- Smart validation for valid dates

### Responsive Design
- Works seamlessly on desktop, tablet, and mobile devices
- Adaptive font sizes and layouts
- Touch-friendly interface

## 🔐 Authentication System

### Registration
1. Click "Don't have an account? Create one"
2. Fill in all required fields:
   - Full Name
   - Email
   - Password (minimum 6 characters)
   - Date of Birth (dd.mm.yyyy format)
3. Click "Register"
4. Automatically logged in after successful registration

### Login
1. Enter your email and password
2. Click "Login"
3. Redirected to dashboard upon success

### Dashboard
- Welcome message with user's name
- Logout functionality
- User session persistence

## 📁 Project Structure

```
royalia/
├── client/                 # Client-side entry points
│   ├── main.html          # HTML template
│   ├── main.jsx           # React app initialization
│   └── main.css           # Global styles & themes
├── imports/               # Shared code
│   ├── api/              # Backend API definitions
│   └── ui/               # React components
│       ├── App.jsx       # Main app with auth logic
│       ├── Landing.jsx   # Login page
│       └── Register.jsx  # Registration page
├── server/               # Server-side code
│   └── main.js          # Server initialization
├── .meteor/             # Meteor configuration
├── package.json         # Dependencies & scripts
└── README.md           # This file
```

## 🗄️ Database

The application uses a **remote MongoDB database** hosted on Kamatera cloud infrastructure.

### Key Collections:
- **users**: User accounts and profiles
- **meteor_accounts_loginServiceConfiguration**: Authentication config

### Connection Details:
- Automatically configured via npm scripts
- No manual database setup required
- Data persists across sessions

## 🎯 Available Scripts

```bash
# Development server (port 3001)
npm run dev

# Production server (port 3000)
npm start

# Run tests
npm test

# Run full-app tests
npm run test-app

# Bundle analyzer
npm run visualize
```

## 🔍 Troubleshooting

### Common Issues:

**1. Port Already in Use**
```bash
# Kill processes on port 3001
lsof -ti:3001 | xargs kill -9
```

**2. Meteor Installation Issues**
```bash
# Reinstall Meteor
curl https://install.meteor.com/ | sh
```

**3. Node Modules Issues**
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install
```

**4. Database Connection Issues**
- Ensure you have internet connectivity
- The remote MongoDB is automatically configured
- No local MongoDB installation required

## 🚀 Deployment

The application is deployment-ready with:
- Remote MongoDB configuration
- Production-optimized builds
- Environment-agnostic setup

For deployment platforms like Heroku, Vercel, or DigitalOcean:
1. The MongoDB connection is pre-configured
2. Use `npm start` as the start command
3. No additional environment variables needed

## 🎨 Customization

### Themes
Edit `client/main.css` to customize:
- Color schemes (CSS variables in `:root` and `[data-theme='dark']`)
- Typography (font families and sizes)
- Layout and spacing

### Components
- **App.jsx**: Main application logic and routing
- **Landing.jsx**: Login form and validation
- **Register.jsx**: Registration form with custom date input

## 📝 Development Notes

### Key Features Implemented:
- ✅ Modern React with hooks
- ✅ Meteor reactive data
- ✅ User authentication system
- ✅ Remote MongoDB integration
- ✅ Responsive design
- ✅ Theme switching
- ✅ Custom form validation
- ✅ Auto-formatting date input

### Future Enhancements:
- Social networking features
- User profiles
- Posts and interactions
- Real-time messaging
- File uploads
- Email verification

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

---

**Built with ❤️ using Meteor.js and React**

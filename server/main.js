import { Meteor } from 'meteor/meteor';
import { Accounts } from 'meteor/accounts-base';
import { PostsCollection } from '/imports/api/posts';
import 'meteor/accounts-password';

Meteor.startup(async () => {
  // Configure accounts
  Accounts.config({
    sendVerificationEmail: false,
    forbidClientAccountCreation: false,
  });  
});

// Methods
Meteor.methods({
  'users.updateBio'(bio) {
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to update your bio');
    }

    return Meteor.users.update(this.userId, {
      $set: {
        'profile.bio': bio
      }
    });
  }
});

// Publish user data
Meteor.publish("userData", function () {
  if (this.userId) {
    return Meteor.users.find(
      { _id: this.userId },
      { fields: { emails: 1, profile: 1 } }
    );
  } else {
    this.ready();
  }
});

// Publish all users for profile lookup
Meteor.publish("allUsers", function () {
  return Meteor.users.find({}, {
    fields: {
      emails: 1,
      profile: 1,
      createdAt: 1
    }
  });
});

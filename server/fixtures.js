import { Meteor } from 'meteor/meteor';
import { Accounts } from 'meteor/accounts-base';
import { PostsCollection } from '/imports/api/posts';

// Create sample users and posts for testing
Meteor.startup(async () => {
  // Only create fixtures in development
  if (Meteor.isDevelopment) {

    // Check if we already have users
    const userCount = await Meteor.users.find().countAsync();
    
    if (userCount === 0) {
      console.log('Creating sample users and posts...');
      
      // Create sample users
      const users = [
        {
          email: '<EMAIL>',
          password: 'password123',
          profile: {
            fullname: '<PERSON>',
            dob: '15.03.1990',
            bio: 'Software developer passionate about creating amazing user experiences. Love hiking, photography, and good coffee.',
            location: 'San Francisco, CA',
            work: 'Senior Developer at TechCorp',
            education: 'Computer Science, Stanford University'
          }
        },
        {
          email: '<EMAIL>',
          password: 'password123',
          profile: {
            fullname: '<PERSON>',
            dob: '22.07.1988',
            bio: 'UX Designer with a passion for creating intuitive and beautiful interfaces. When not designing, you can find me painting or exploring new cafes.',
            location: 'New York, NY',
            work: 'Lead UX Designer at DesignStudio',
            education: 'Design, Parsons School of Design'
          }
        },
        {
          email: '<EMAIL>',
          password: 'password123',
          profile: {
            fullname: 'Alex Chen',
            dob: '10.11.1992',
            bio: 'Full-stack developer and tech enthusiast. Building the future one line of code at a time. Love gaming, anime, and spicy food.',
            location: 'Seattle, WA',
            work: 'Software Engineer at StartupXYZ',
            education: 'Computer Engineering, University of Washington'
          }
        }
      ];

      const createdUsers = [];
      
      users.forEach(userData => {
        try {
          const userId = Accounts.createUser({
            email: userData.email,
            password: userData.password,
            profile: userData.profile
          });
          createdUsers.push(userId);
          console.log(`Created user: ${userData.profile.fullname}`);
        } catch (error) {
          console.log(`Error creating user ${userData.profile.fullname}:`, error);
        }
      });

      // Wait a bit for users to be created, then add sample posts
      Meteor.setTimeout(() => {
        const allUsers = Meteor.users.find().fetch();
        
        if (allUsers.length > 0) {
          // Sample posts
          const samplePosts = [
            {
              content: "Just finished building an amazing new feature for our app! The user feedback has been incredible. There's nothing quite like seeing your code make a real difference in people's lives. 🚀",
              images: []
            },
            {
              content: "Beautiful sunset from my hike this weekend. Sometimes you need to step away from the screen and appreciate the world around us. Nature is the best debugger! 🌅",
              images: []
            },
            {
              content: "Working on some exciting new designs today. The intersection of technology and human psychology never ceases to amaze me. Every pixel matters when you're crafting experiences that people will use every day.",
              images: []
            },
            {
              content: "Coffee shop coding session complete! ☕ There's something magical about the ambient noise and energy of a good cafe. Plus, the wifi here is surprisingly fast!",
              images: []
            },
            {
              content: "Just deployed our latest update to production. Zero downtime, smooth rollout, and the monitoring dashboards are looking green across the board. DevOps done right! 💚",
              images: []
            },
            {
              content: "Attended an amazing tech meetup tonight. The community here is incredible - so many brilliant minds working on fascinating problems. Already planning to implement some of the ideas I learned!",
              images: []
            }
          ];

          // Create posts for each user
          allUsers.forEach((user, userIndex) => {
            // Each user gets 2-3 posts
            const userPosts = samplePosts.slice(userIndex * 2, (userIndex * 2) + 2);
            
            userPosts.forEach((postData, postIndex) => {
              try {
                // Create post with a slight delay to simulate realistic timing
                Meteor.setTimeout(() => {
                  const postId = PostsCollection.insert({
                    userId: user._id,
                    author: {
                      fullname: user.profile?.fullname || user.emails?.[0]?.address,
                      email: user.emails?.[0]?.address
                    },
                    content: postData.content,
                    images: postData.images,
                    likes: [],
                    comments: [],
                    createdAt: new Date(Date.now() - (userIndex * 86400000) - (postIndex * 3600000)), // Spread posts over time
                    updatedAt: new Date(Date.now() - (userIndex * 86400000) - (postIndex * 3600000))
                  });
                  
                  console.log(`Created post for ${user.profile?.fullname}: ${postData.content.substring(0, 50)}...`);
                }, postIndex * 100);
              } catch (error) {
                console.log(`Error creating post for ${user.profile?.fullname}:`, error);
              }
            });
          });

          console.log('Sample data creation complete!');
        }
      }, 1000);
    }
  }
});

import React, { useState } from 'react';

export const PhotosSection = ({ profileUser, posts, isOwnProfile, expanded = false }) => {
  const [selectedPhoto, setSelectedPhoto] = useState(null);
  const [showUpload, setShowUpload] = useState(false);

  // Extract all images from posts
  const allPhotos = posts.reduce((photos, post) => {
    if (post.images && post.images.length > 0) {
      const postPhotos = post.images.map(image => ({
        url: image,
        postId: post._id,
        createdAt: post.createdAt,
        caption: post.content
      }));
      return [...photos, ...postPhotos];
    }
    return photos;
  }, []);

  // Sort photos by date (newest first)
  allPhotos.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

  const displayPhotos = expanded ? allPhotos : allPhotos.slice(0, 9);

  const handlePhotoUpload = (event) => {
    const files = Array.from(event.target.files);
    if (files.length > 0) {
      // TODO: Implement photo upload functionality
      console.log('Photo upload:', files);
      setShowUpload(false);
    }
  };

  const openLightbox = (photo) => {
    setSelectedPhoto(photo);
  };

  const closeLightbox = () => {
    setSelectedPhoto(null);
  };

  const navigatePhoto = (direction) => {
    const currentIndex = allPhotos.findIndex(photo => photo.url === selectedPhoto.url);
    let newIndex;
    
    if (direction === 'next') {
      newIndex = (currentIndex + 1) % allPhotos.length;
    } else {
      newIndex = currentIndex === 0 ? allPhotos.length - 1 : currentIndex - 1;
    }
    
    setSelectedPhoto(allPhotos[newIndex]);
  };

  return (
    <div className="photos-section">
      <div className="card">
        <div className="card-header">
          <h3>Photos</h3>
          <div className="photos-actions">
            {allPhotos.length > 0 && !expanded && (
              <button className="btn-text">
                See All ({allPhotos.length})
              </button>
            )}
            {isOwnProfile && (
              <button 
                className="btn-text"
                onClick={() => setShowUpload(true)}
              >
                Add Photos
              </button>
            )}
          </div>
        </div>

        <div className="photos-content">
          {displayPhotos.length > 0 ? (
            <div className={`photos-grid ${expanded ? 'expanded' : ''}`}>
              {displayPhotos.map((photo, index) => (
                <div 
                  key={`${photo.postId}-${index}`}
                  className="photo-item"
                  onClick={() => openLightbox(photo)}
                >
                  <img 
                    src={photo.url} 
                    alt={`Photo ${index + 1}`}
                    className="photo-thumbnail"
                  />
                  <div className="photo-overlay">
                    <div className="photo-date">
                      {new Date(photo.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-photos">
              <div className="no-photos-icon">📷</div>
              <p>
                {isOwnProfile 
                  ? "You haven't shared any photos yet" 
                  : "No photos to show"
                }
              </p>
              {isOwnProfile && (
                <button 
                  className="btn"
                  onClick={() => setShowUpload(true)}
                >
                  Add Your First Photo
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Photo Upload Modal */}
      {showUpload && (
        <div className="upload-modal">
          <div className="upload-modal-content">
            <div className="upload-modal-header">
              <h3>Add Photos</h3>
              <button 
                className="close-btn"
                onClick={() => setShowUpload(false)}
              >
                ✕
              </button>
            </div>
            <div className="upload-modal-body">
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={handlePhotoUpload}
                className="file-input"
                id="photos-upload"
              />
              <label htmlFor="photos-upload" className="file-label">
                <div className="upload-area">
                  <div className="upload-icon">📷</div>
                  <p>Click to select photos</p>
                  <small>You can select multiple photos at once</small>
                </div>
              </label>
            </div>
          </div>
          <div 
            className="upload-modal-backdrop"
            onClick={() => setShowUpload(false)}
          ></div>
        </div>
      )}

      {/* Photo Lightbox */}
      {selectedPhoto && (
        <div className="lightbox">
          <div className="lightbox-content">
            <button 
              className="lightbox-close"
              onClick={closeLightbox}
            >
              ✕
            </button>
            
            {allPhotos.length > 1 && (
              <>
                <button 
                  className="lightbox-nav lightbox-prev"
                  onClick={() => navigatePhoto('prev')}
                >
                  ‹
                </button>
                <button 
                  className="lightbox-nav lightbox-next"
                  onClick={() => navigatePhoto('next')}
                >
                  ›
                </button>
              </>
            )}

            <div className="lightbox-image-container">
              <img 
                src={selectedPhoto.url} 
                alt="Full size photo"
                className="lightbox-image"
              />
            </div>

            <div className="lightbox-info">
              <div className="lightbox-date">
                {new Date(selectedPhoto.createdAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
              {selectedPhoto.caption && (
                <div className="lightbox-caption">
                  {selectedPhoto.caption}
                </div>
              )}
            </div>
          </div>
          <div 
            className="lightbox-backdrop"
            onClick={closeLightbox}
          ></div>
        </div>
      )}
    </div>
  );
};

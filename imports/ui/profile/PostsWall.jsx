import React, { useState } from 'react';
import { Meteor } from 'meteor/meteor';
import { getUserInitials, formatProfileDate } from '../../utils/profileUtils';

export const PostsWall = ({ posts, profileUser, currentUser, isOwnProfile }) => {
  const [commentInputs, setCommentInputs] = useState({});
  const [showComments, setShowComments] = useState({});

  const handleLike = (postId) => {
    Meteor.call('posts.like', postId, (err) => {
      if (err) {
        console.error('Error liking post:', err);
      }
    });
  };

  const handleComment = (postId) => {
    const content = commentInputs[postId];
    if (!content || !content.trim()) return;

    Meteor.call('posts.addComment', postId, content.trim(), (err) => {
      if (err) {
        console.error('Error adding comment:', err);
      } else {
        setCommentInputs({ ...commentInputs, [postId]: '' });
      }
    });
  };

  const handleDeletePost = (postId) => {
    if (confirm('Are you sure you want to delete this post?')) {
      Meteor.call('posts.remove', postId, (err) => {
        if (err) {
          console.error('Error deleting post:', err);
        }
      });
    }
  };

  const handleDeleteComment = (postId, commentId) => {
    if (confirm('Are you sure you want to delete this comment?')) {
      Meteor.call('posts.removeComment', postId, commentId, (err) => {
        if (err) {
          console.error('Error deleting comment:', err);
        }
      });
    }
  };

  const toggleComments = (postId) => {
    setShowComments({
      ...showComments,
      [postId]: !showComments[postId]
    });
  };

  const updateCommentInput = (postId, value) => {
    setCommentInputs({
      ...commentInputs,
      [postId]: value
    });
  };

  const isLikedByCurrentUser = (post) => {
    return currentUser && post.likes.some(like => like.userId === currentUser._id);
  };

  const formatTimeAgo = (date) => {
    const now = new Date();
    const postDate = new Date(date);
    const diffInSeconds = Math.floor((now - postDate) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
    
    return formatProfileDate(postDate);
  };

  if (posts.length === 0) {
    return (
      <div className="no-posts">
        <div className="no-posts-icon">📝</div>
        <h3>
          {isOwnProfile 
            ? "You haven't shared anything yet" 
            : `${profileUser.profile?.fullname} hasn't shared anything yet`
          }
        </h3>
        <p>
          {isOwnProfile 
            ? "Share your first post to get started!" 
            : "Check back later for updates"
          }
        </p>
      </div>
    );
  }

  return (
    <div className="posts-wall">
      {posts.map((post) => (
        <div key={post._id} className="post-card">
          {/* Post Header */}
          <div className="post-header">
            <div className="post-author">
              <div className="author-avatar">
                {profileUser.profile?.profilePicture ? (
                  <img 
                    src={profileUser.profile.profilePicture} 
                    alt={post.author.fullname}
                  />
                ) : (
                  <div className="avatar-placeholder">
                    {getUserInitials(post.author.fullname)}
                  </div>
                )}
              </div>
              <div className="author-info">
                <div className="author-name">{post.author.fullname}</div>
                <div className="post-time">{formatTimeAgo(post.createdAt)}</div>
              </div>
            </div>
            
            {(isOwnProfile || (currentUser && currentUser._id === post.userId)) && (
              <div className="post-actions">
                <button 
                  className="post-action-btn"
                  onClick={() => handleDeletePost(post._id)}
                >
                  🗑️
                </button>
              </div>
            )}
          </div>

          {/* Post Content */}
          <div className="post-content">
            <p className="post-text">{post.content}</p>
            
            {post.images && post.images.length > 0 && (
              <div className={`post-images ${post.images.length === 1 ? 'single' : 'multiple'}`}>
                {post.images.map((image, index) => (
                  <img 
                    key={index}
                    src={image} 
                    alt={`Post image ${index + 1}`}
                    className="post-image"
                  />
                ))}
              </div>
            )}
          </div>

          {/* Post Stats */}
          <div className="post-stats">
            <div className="stats-left">
              {post.likes.length > 0 && (
                <span className="like-count">
                  👍 {post.likes.length} {post.likes.length === 1 ? 'like' : 'likes'}
                </span>
              )}
            </div>
            <div className="stats-right">
              {post.comments.length > 0 && (
                <button 
                  className="comment-count"
                  onClick={() => toggleComments(post._id)}
                >
                  {post.comments.length} {post.comments.length === 1 ? 'comment' : 'comments'}
                </button>
              )}
            </div>
          </div>

          {/* Post Actions */}
          <div className="post-interactions">
            <button 
              className={`interaction-btn ${isLikedByCurrentUser(post) ? 'liked' : ''}`}
              onClick={() => handleLike(post._id)}
              disabled={!currentUser}
            >
              👍 Like
            </button>
            <button 
              className="interaction-btn"
              onClick={() => toggleComments(post._id)}
            >
              💬 Comment
            </button>
            <button className="interaction-btn">
              📤 Share
            </button>
          </div>

          {/* Comments Section */}
          {(showComments[post._id] || post.comments.length > 0) && (
            <div className="comments-section">
              {post.comments.map((comment) => (
                <div key={comment._id} className="comment">
                  <div className="comment-avatar">
                    <div className="avatar-placeholder">
                      {getUserInitials(comment.author.fullname)}
                    </div>
                  </div>
                  <div className="comment-content">
                    <div className="comment-bubble">
                      <div className="comment-author">{comment.author.fullname}</div>
                      <div className="comment-text">{comment.content}</div>
                    </div>
                    <div className="comment-actions">
                      <span className="comment-time">
                        {formatTimeAgo(comment.createdAt)}
                      </span>
                      {currentUser && (currentUser._id === comment.userId || currentUser._id === post.userId) && (
                        <button 
                          className="comment-delete"
                          onClick={() => handleDeleteComment(post._id, comment._id)}
                        >
                          Delete
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Add Comment */}
              {currentUser && (
                <div className="add-comment">
                  <div className="comment-avatar">
                    <div className="avatar-placeholder">
                      {getUserInitials(currentUser.profile?.fullname)}
                    </div>
                  </div>
                  <div className="comment-input-container">
                    <input
                      type="text"
                      placeholder="Write a comment..."
                      value={commentInputs[post._id] || ''}
                      onChange={(e) => updateCommentInput(post._id, e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          handleComment(post._id);
                        }
                      }}
                      className="comment-input"
                    />
                    <button 
                      className="comment-submit"
                      onClick={() => handleComment(post._id)}
                      disabled={!commentInputs[post._id]?.trim()}
                    >
                      Post
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

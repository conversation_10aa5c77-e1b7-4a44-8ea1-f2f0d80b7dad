import React, { useState } from 'react';
import { Meteor } from 'meteor/meteor';

export const EditProfileModal = ({ user, isOpen, onClose, onThemeToggle, currentTheme }) => {
  const [formData, setFormData] = useState({
    fullname: user.profile?.fullname || '',
    bio: user.profile?.bio || '',
    location: user.profile?.location || '',
    work: user.profile?.work || '',
    education: user.profile?.education || '',
    dob: user.profile?.dob || ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDateChange = (e) => {
    let value = e.target.value.replace(/\D/g, ''); // Remove all non-digits

    // Format as dd.mm.yyyy
    if (value.length >= 2) {
      value = value.substring(0, 2) + '.' + value.substring(2);
    }
    if (value.length >= 5) {
      value = value.substring(0, 5) + '.' + value.substring(5, 9);
    }

    // Limit to 10 characters (dd.mm.yyyy)
    if (value.length > 10) {
      value = value.substring(0, 10);
    }

    setFormData(prev => ({
      ...prev,
      dob: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Validate required fields
    if (!formData.fullname.trim()) {
      setError('Full name is required');
      setIsLoading(false);
      return;
    }

    // Validate date format if provided
    if (formData.dob && formData.dob.length !== 10) {
      setError('Please enter a valid date of birth (dd.mm.yyyy)');
      setIsLoading(false);
      return;
    }

    Meteor.call('users.updateProfile', formData, (err) => {
      setIsLoading(false);
      if (err) {
        setError(err.reason || 'Failed to update profile');
      } else {
        onClose();
      }
    });
  };

  const handleLogout = () => {
    Meteor.logout();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="edit-profile-modal">
      <div className="modal-backdrop" onClick={onClose}></div>
      <div className="modal-content">
        <div className="modal-header">
          <h2>Edit Profile</h2>
          <button className="close-btn" onClick={onClose}>✕</button>
        </div>

        <form onSubmit={handleSubmit} className="edit-profile-form">
          <div className="form-section">
            <h3>Basic Information</h3>
            
            <div className="form-group">
              <label>Full Name</label>
              <input
                type="text"
                name="fullname"
                value={formData.fullname}
                onChange={handleChange}
                placeholder="Enter your full name"
                required
              />
            </div>

            <div className="form-group">
              <label>Date of Birth (dd.mm.yyyy)</label>
              <input
                type="text"
                name="dob"
                value={formData.dob}
                onChange={handleDateChange}
                placeholder="15.03.1990"
                maxLength={10}
              />
            </div>

            <div className="form-group">
              <label>Bio</label>
              <textarea
                name="bio"
                value={formData.bio}
                onChange={handleChange}
                placeholder="Tell people about yourself..."
                rows={4}
                maxLength={500}
              />
              <small className="char-count">{formData.bio.length}/500 characters</small>
            </div>
          </div>

          <div className="form-section">
            <h3>Additional Information</h3>
            
            <div className="form-group">
              <label>Location</label>
              <input
                type="text"
                name="location"
                value={formData.location}
                onChange={handleChange}
                placeholder="City, Country"
              />
            </div>

            <div className="form-group">
              <label>Work</label>
              <input
                type="text"
                name="work"
                value={formData.work}
                onChange={handleChange}
                placeholder="Job title at Company"
              />
            </div>

            <div className="form-group">
              <label>Education</label>
              <input
                type="text"
                name="education"
                value={formData.education}
                onChange={handleChange}
                placeholder="Degree, University"
              />
            </div>
          </div>

          <div className="form-section">
            <h3>Preferences</h3>
            
            <div className="form-group">
              <label>Theme</label>
              <div className="theme-selector">
                <button
                  type="button"
                  className={`theme-option ${currentTheme === 'light' ? 'active' : ''}`}
                  onClick={() => onThemeToggle('light')}
                >
                  ☀️ Light
                </button>
                <button
                  type="button"
                  className={`theme-option ${currentTheme === 'dark' ? 'active' : ''}`}
                  onClick={() => onThemeToggle('dark')}
                >
                  🌙 Dark
                </button>
              </div>
            </div>
          </div>

          {error && (
            <div className="form-error">
              <p className="error">{error}</p>
            </div>
          )}

          <div className="form-actions">
            <div className="action-group">
              <button
                type="button"
                className="btn-secondary"
                onClick={onClose}
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn"
                disabled={isLoading}
              >
                {isLoading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
            
            <div className="danger-zone">
              <button
                type="button"
                className="btn-danger"
                onClick={handleLogout}
              >
                Logout
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

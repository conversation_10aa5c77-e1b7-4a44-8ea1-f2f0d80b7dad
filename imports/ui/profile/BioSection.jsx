import React, { useState } from 'react';
import { Meteor } from 'meteor/meteor';

export const BioSection = ({ profileUser, joinDate, age, isOwnProfile }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editBio, setEditBio] = useState(profileUser.profile?.bio || '');
  const [error, setError] = useState('');

  const handleSaveBio = () => {
    setError('');
    
    Meteor.call('users.updateBio', editBio, (err) => {
      if (err) {
        setError(err.reason || 'Failed to update bio');
      } else {
        setIsEditing(false);
      }
    });
  };

  const handleCancelEdit = () => {
    setEditBio(profileUser.profile?.bio || '');
    setIsEditing(false);
    setError('');
  };

  return (
    <div className="bio-section">
      <div className="card">
        <div className="card-header">
          <h3>About</h3>
          {isOwnProfile && !isEditing && (
            <button 
              className="btn-text"
              onClick={() => setIsEditing(true)}
            >
              Edit
            </button>
          )}
        </div>

        <div className="bio-content">
          {isEditing ? (
            <div className="bio-edit">
              <textarea
                value={editBio}
                onChange={(e) => setEditBio(e.target.value)}
                placeholder="Tell people about yourself..."
                className="bio-textarea"
                maxLength={500}
                rows={4}
              />
              <div className="bio-edit-actions">
                <small className="char-count">
                  {editBio.length}/500 characters
                </small>
                {error && <p className="error">{error}</p>}
                <div className="bio-buttons">
                  <button 
                    className="btn-secondary"
                    onClick={handleCancelEdit}
                  >
                    Cancel
                  </button>
                  <button 
                    className="btn"
                    onClick={handleSaveBio}
                  >
                    Save
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="bio-display">
              {profileUser.profile?.bio ? (
                <p className="bio-text">{profileUser.profile.bio}</p>
              ) : (
                <p className="bio-empty">
                  {isOwnProfile 
                    ? "Add a bio to tell people about yourself" 
                    : "No bio available"
                  }
                </p>
              )}
            </div>
          )}
        </div>

        <div className="profile-details">
          <div className="detail-item">
            <div className="detail-icon">🎂</div>
            <div className="detail-content">
              <strong>Date of Birth</strong>
              <span>
                {profileUser.profile?.dob || 'Not specified'}
                {age && ` (${age} years old)`}
              </span>
            </div>
          </div>

          <div className="detail-item">
            <div className="detail-icon">📅</div>
            <div className="detail-content">
              <strong>Joined</strong>
              <span>{joinDate}</span>
            </div>
          </div>

          <div className="detail-item">
            <div className="detail-icon">📧</div>
            <div className="detail-content">
              <strong>Email</strong>
              <span>{profileUser.emails?.[0]?.address}</span>
            </div>
          </div>

          <div className="detail-item">
            <div className="detail-icon">🌍</div>
            <div className="detail-content">
              <strong>Location</strong>
              <span>{profileUser.profile?.location || 'Not specified'}</span>
            </div>
          </div>

          <div className="detail-item">
            <div className="detail-icon">💼</div>
            <div className="detail-content">
              <strong>Work</strong>
              <span>{profileUser.profile?.work || 'Not specified'}</span>
            </div>
          </div>

          <div className="detail-item">
            <div className="detail-icon">🎓</div>
            <div className="detail-content">
              <strong>Education</strong>
              <span>{profileUser.profile?.education || 'Not specified'}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats Card */}
      <div className="card stats-card">
        <h3>Activity</h3>
        <div className="stats-grid">
          <div className="stat-item">
            <div className="stat-number">0</div>
            <div className="stat-label">Posts</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">0</div>
            <div className="stat-label">Friends</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">0</div>
            <div className="stat-label">Photos</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">0</div>
            <div className="stat-label">Likes</div>
          </div>
        </div>
      </div>
    </div>
  );
};

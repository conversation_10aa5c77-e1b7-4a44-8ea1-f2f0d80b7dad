import React, { useState } from 'react';
import { Meteor } from 'meteor/meteor';
import { getUserInitials } from '../../utils/profileUtils';

export const NewPostForm = () => {
  const [content, setContent] = useState('');
  const [images, setImages] = useState([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isPosting, setIsPosting] = useState(false);
  const [error, setError] = useState('');

  const currentUser = Meteor.user();

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!content.trim() && images.length === 0) {
      setError('Please add some content or images to your post');
      return;
    }

    setIsPosting(true);
    setError('');

    // Convert images to base64 or upload URLs (simplified for now)
    const imageUrls = images.map(img => img.url || img);

    Meteor.call('posts.insert', content.trim(), imageUrls, (err) => {
      setIsPosting(false);
      
      if (err) {
        setError(err.reason || 'Failed to create post');
      } else {
        // Reset form
        setContent('');
        setImages([]);
        setIsExpanded(false);
        setError('');
      }
    });
  };

  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files);
    
    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (event) => {
          const newImage = {
            file,
            url: event.target.result,
            name: file.name
          };
          setImages(prev => [...prev, newImage]);
        };
        reader.readAsDataURL(file);
      }
    });
  };

  const removeImage = (index) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleFocus = () => {
    setIsExpanded(true);
  };

  const handleCancel = () => {
    setContent('');
    setImages([]);
    setIsExpanded(false);
    setError('');
  };

  return (
    <div className="new-post-form">
      <div className="card">
        <form onSubmit={handleSubmit}>
          {/* Post Header */}
          <div className="post-form-header">
            <div className="user-avatar">
              {currentUser?.profile?.profilePicture ? (
                <img 
                  src={currentUser.profile.profilePicture} 
                  alt="Your profile"
                />
              ) : (
                <div className="avatar-placeholder">
                  {getUserInitials(currentUser?.profile?.fullname)}
                </div>
              )}
            </div>
            <div className="post-form-input">
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                onFocus={handleFocus}
                placeholder={`What's on your mind, ${currentUser?.profile?.fullname?.split(' ')[0] || 'there'}?`}
                className={`post-textarea ${isExpanded ? 'expanded' : ''}`}
                rows={isExpanded ? 4 : 1}
                maxLength={2000}
              />
            </div>
          </div>

          {/* Image Preview */}
          {images.length > 0 && (
            <div className="image-preview">
              <div className="image-grid">
                {images.map((image, index) => (
                  <div key={index} className="image-preview-item">
                    <img src={image.url} alt={`Upload ${index + 1}`} />
                    <button
                      type="button"
                      className="remove-image"
                      onClick={() => removeImage(index)}
                    >
                      ✕
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="post-error">
              <p className="error">{error}</p>
            </div>
          )}

          {/* Post Actions */}
          {isExpanded && (
            <div className="post-form-actions">
              <div className="post-options">
                <label className="post-option">
                  <input
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleImageUpload}
                    style={{ display: 'none' }}
                  />
                  <span className="option-icon">📷</span>
                  <span className="option-text">Photo</span>
                </label>

                <button type="button" className="post-option" disabled>
                  <span className="option-icon">😊</span>
                  <span className="option-text">Feeling</span>
                </button>

                <button type="button" className="post-option" disabled>
                  <span className="option-icon">📍</span>
                  <span className="option-text">Location</span>
                </button>

                <button type="button" className="post-option" disabled>
                  <span className="option-icon">🏷️</span>
                  <span className="option-text">Tag</span>
                </button>
              </div>

              <div className="post-form-buttons">
                <div className="privacy-selector">
                  <select className="privacy-select" disabled>
                    <option value="public">🌍 Public</option>
                    <option value="friends">👥 Friends</option>
                    <option value="private">🔒 Only me</option>
                  </select>
                </div>

                <div className="submit-buttons">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={handleCancel}
                    disabled={isPosting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn"
                    disabled={isPosting || (!content.trim() && images.length === 0)}
                  >
                    {isPosting ? 'Posting...' : 'Post'}
                  </button>
                </div>
              </div>

              <div className="character-count">
                <small>
                  {content.length}/2000 characters
                </small>
              </div>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

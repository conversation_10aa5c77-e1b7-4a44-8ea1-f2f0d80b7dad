import React, { useState } from 'react';
import { Accounts } from 'meteor/accounts-base';

export const Register = ({ goToLanding, theme, onThemeToggle }) => {
  const [register, setRegister] = useState({ fullname: '', email: '', password: '', dob: '' });
  const [error, setError] = useState('');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setRegister({ ...register, [name]: value });
  };

  const handleDateChange = (e) => {
    let value = e.target.value.replace(/\D/g, ''); // Remove all non-digits

    // Format as dd.mm.yyyy
    if (value.length >= 2) {
      value = value.substring(0, 2) + '.' + value.substring(2);
    }
    if (value.length >= 5) {
      value = value.substring(0, 5) + '.' + value.substring(5, 9);
    }

    // Limit to 10 characters (dd.mm.yyyy)
    if (value.length > 10) {
      value = value.substring(0, 10);
    }

    setRegister({ ...register, dob: value });
  };

  const handleRegister = (e) => {
    e.preventDefault();
    setError(''); // Clear any previous errors

    // Validation
    if (!register.fullname.trim()) {
      setError('Full name is required');
      return;
    }
    const emailRegex = /^[^@]+@[^@]+\.[^@]+$/;
    if (!emailRegex.test(register.email)) {
      setError('Please enter a valid email');
      return;
    }
    if (register.password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }
    if (!register.dob || register.dob.length !== 10) {
      setError('Please enter a valid date of birth (dd.mm.yyyy)');
      return;
    }

    // Validate date format and values
    const dateParts = register.dob.split('.');
    if (dateParts.length !== 3) {
      setError('Please enter date in dd.mm.yyyy format');
      return;
    }

    const day = parseInt(dateParts[0], 10);
    const month = parseInt(dateParts[1], 10);
    const year = parseInt(dateParts[2], 10);

    if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > new Date().getFullYear()) {
      setError('Please enter a valid date');
      return;
    }

    // Check if the date is valid (handles leap years, etc.)
    const dateObj = new Date(year, month - 1, day);
    if (dateObj.getDate() !== day || dateObj.getMonth() !== month - 1 || dateObj.getFullYear() !== year) {
      setError('Please enter a valid date');
      return;
    }

    Accounts.createUser(
      {
        email: register.email,
        password: register.password,
        profile: {
          fullname: register.fullname,
          dob: register.dob,
        },
      },
      (err) => {
        if (err) {
          setError(err.reason || 'Registration failed');
        } else {
          setError('');
          setRegister({ fullname: '', email: '', password: '', dob: '' });
          // The App component will automatically redirect to dashboard after successful registration
        }
      }
    );
  };

  return (
    <div className="landing">
      <button className="theme-toggle" onClick={onThemeToggle} aria-label="Toggle theme">
        {theme === 'light' ? '🌙' : '☀️'}
      </button>
      <h1 className="title brand">Roylia</h1>
      <p className="tagline">Rule with your mind.</p>
      <form className="card" onSubmit={handleRegister}>
        <h2>Register</h2>
        {error && <p className="error">{error}</p>}
        <label>
          Full Name
          <input
            type="text"
            name="fullname"
            value={register.fullname}
            onChange={handleChange}
            required
          />
        </label>
        <label>
          Email
          <input
            type="email"
            name="email"
            value={register.email}
            onChange={handleChange}
            pattern="[^@]+@[^@]+\.[^@]+"
            required
          />
        </label>
        <label>
          Password
          <input
            type="password"
            name="password"
            value={register.password}
            onChange={handleChange}
            minLength={6}
            required
          />
        </label>
        <label>
          Date of Birth (dd.mm.yyyy)
          <input
            type="text"
            name="dob"
            value={register.dob}
            onChange={handleDateChange}
            placeholder="dd.mm.yyyy"
            maxLength="10"
            required
          />
        </label>
        <button type="submit" className="btn">Register</button>
      </form>
      <div className="forget">
        <a href="#" onClick={goToLanding}>Back to Login</a>
      </div>
    </div>
  );
};

import React, { useEffect, useState } from 'react';
import { useTracker } from 'meteor/react-meteor-data';
import { Meteor } from 'meteor/meteor';
import { Landing } from './Landing.jsx';
import { Register } from './Register.jsx';
import { UserProfile } from './UserProfile.jsx';
import { generateProfileUrl } from '../utils/profileUtils.js';

export const App = () => {
  const [theme, setTheme] = useState('light');
  const [page, setPage] = useState('landing');
  const [currentRoute, setCurrentRoute] = useState(window.location.pathname);

  // Track user authentication state
  const { user, isLoading } = useTracker(() => {
    const user = Meteor.user();
    const isLoading = Meteor.loggingIn();
    return { user, isLoading };
  }, []);

  useEffect(() => {
    document.body.dataset.theme = theme;
  }, [theme]);

  // Handle browser navigation
  useEffect(() => {
    const handlePopState = () => {
      setCurrentRoute(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // Navigation helper
  const navigateTo = (path) => {
    window.history.pushState({}, '', path);
    setCurrentRoute(path);
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const handleLogout = () => {
    Meteor.logout();
    setPage('landing');
    navigateTo('/');
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="app">
        <div className="landing">
          <h1 className="title brand">Roylia</h1>
          <p className="tagline">Loading...</p>
        </div>
      </div>
    );
  }

  // Show user content if logged in
  if (user) {
    // Parse current route
    const pathParts = currentRoute.split('/').filter(part => part);

    // Check if it's a profile route
    if (pathParts[0] === 'profile' && pathParts[1]) {
      const profileUrl = pathParts[1];
      return (
        <div className="app">
          <div className="app-header">
            <div className="app-nav">
              <h1 className="brand" onClick={() => navigateTo('/')}>Roylia</h1>
              <div className="nav-actions">
                <button
                  className="nav-btn"
                  onClick={() => {
                    const userProfileUrl = generateProfileUrl(user.profile?.fullname);
                    navigateTo(`/profile/${userProfileUrl}`);
                  }}
                >
                  My Profile
                </button>
                <button className="nav-btn" onClick={handleLogout}>Logout</button>
                <button className="theme-toggle" onClick={toggleTheme} aria-label="Toggle theme">
                  {theme === 'light' ? '🌙' : '☀️'}
                </button>
              </div>
            </div>
          </div>
          <UserProfile profileUrl={profileUrl} currentUser={user} />
        </div>
      );
    }

    // Default dashboard/home view
    return (
      <div className="app">
        <div className="app-header">
          <div className="app-nav">
            <h1 className="brand">Roylia</h1>
            <div className="nav-actions">
              <button
                className="nav-btn"
                onClick={() => {
                  const userProfileUrl = generateProfileUrl(user.profile?.fullname);
                  navigateTo(`/profile/${userProfileUrl}`);
                }}
              >
                My Profile
              </button>
              <button className="nav-btn" onClick={handleLogout}>Logout</button>
              <button className="theme-toggle" onClick={toggleTheme} aria-label="Toggle theme">
                {theme === 'light' ? '🌙' : '☀️'}
              </button>
            </div>
          </div>
        </div>
        <div className="dashboard">
          <div className="welcome-section">
            <h2>Welcome to Roylia, {user.profile?.fullname || user.emails?.[0]?.address}!</h2>
            <p>The social network for the mentally royal.</p>
            <button
              className="btn"
              onClick={() => {
                const userProfileUrl = generateProfileUrl(user.profile?.fullname);
                navigateTo(`/profile/${userProfileUrl}`);
              }}
            >
              Go to My Profile
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show login/register pages if user is not logged in
  return (
    <div className="app">
      <button className="theme-toggle" onClick={toggleTheme} aria-label="Toggle theme">
        {theme === 'light' ? '🌙' : '☀️'}
      </button>
      {page === 'landing' && <Landing goToRegister={() => setPage('register')} />}
      {page === 'register' && <Register goToLanding={() => setPage('landing')} />}
    </div>
  );
};

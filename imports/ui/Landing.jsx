import React, { useState } from 'react';
import { Meteor } from 'meteor/meteor';

export const Landing = ({ goToRegister, theme, onThemeToggle }) => {
  const [login, setLogin] = useState({ email: '', password: '' });
  const [error, setError] = useState('');

  const handleLoginChange = (e) => {
    const { name, value } = e.target;
    setLogin({ ...login, [name]: value });
  };

  const handleLogin = (e) => {
    e.preventDefault();
    setError(''); // Clear any previous errors

    Meteor.loginWithPassword(login.email, login.password, (err) => {
      if (err) {
        setError(err.reason || 'Lo<PERSON> failed');
      } else {
        setError('');
        setLogin({ email: '', password: '' }); // Clear form on success
        // The App component will automatically redirect to dashboard
      }
    });
  };

  return (
    <div className="landing">
      <button className="theme-toggle" onClick={onThemeToggle} aria-label="Toggle theme">
        {theme === 'light' ? '🌙' : '☀️'}
      </button>
      <h1 className="title brand">Roylia</h1>
      <p className="tagline">The social network for the mentally royal</p>
      <div className="forms">
        <form className="card" onSubmit={handleLogin}>
          <h2>Login</h2>
          {error && <p className="error">{error}</p>}
          <label>
            Email
            <input
              type="email"
              name="email"
              value={login.email}
              onChange={handleLoginChange}
              pattern="[^@]+@[^@]+\.[^@]+"
              required
            />
          </label>
          <label>
            Password
            <input
              type="password"
              name="password"
              value={login.password}
              onChange={handleLoginChange}
              minLength={6}
              required
            />
          </label>
          <button type="submit" className="btn">Login</button>
          <div className="forget">
            <a href="#">Forgot password?</a>
          </div>
        </form>
      </div>
      <div className="forget">
        <a href="#" onClick={goToRegister}>Don't have an account? Create one.</a>
      </div>
    </div>
  );
};

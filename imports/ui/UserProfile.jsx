import React, { useState } from 'react';
import { useTracker } from 'meteor/react-meteor-data';
import { Meteor } from 'meteor/meteor';
import { PostsCollection } from '../api/posts';
import { generateProfileUrl, getDisplayName, getUserInitials, formatProfileDate, calculateAge } from '../utils/profileUtils';
import { CoverPhotoSection } from './profile/CoverPhotoSection';
import { BioSection } from './profile/BioSection';
import { PhotosSection } from './profile/PhotosSection';
import { PostsWall } from './profile/PostsWall';
import { NewPostForm } from './profile/NewPostForm';

export const UserProfile = ({ profileUrl, currentUser, onThemeToggle, currentTheme }) => {
  const [activeTab, setActiveTab] = useState('posts');

  // Find the profile user based on the URL
  const { profileUser, posts, isLoading } = useTracker(() => {
    // Subscribe to user data and posts
    const userSub = Meteor.subscribe('userData');
    const postsSub = Meteor.subscribe('posts');
    
    // Find all users to match profile URL
    const allUsers = Meteor.users.find({}, {
      fields: { 'profile.fullname': 1, emails: 1, createdAt: 1, 'profile.dob': 1, 'profile.bio': 1 }
    }).fetch();

    // Find user whose fullname generates the matching profile URL
    const profileUser = allUsers.find(user => {
      const userProfileUrl = generateProfileUrl(user.profile?.fullname);
      return userProfileUrl === profileUrl;
    });

    let posts = [];
    if (profileUser) {
      posts = PostsCollection.find(
        { userId: profileUser._id },
        { sort: { createdAt: -1 } }
      ).fetch();
    }

    return {
      profileUser,
      posts,
      isLoading: !userSub.ready() || !postsSub.ready()
    };
  }, [profileUrl]);

  if (isLoading) {
    return (
      <div className="profile-loading">
        <div className="loading-spinner"></div>
        <p>Loading profile...</p>
      </div>
    );
  }

  if (!profileUser) {
    return (
      <div className="profile-not-found">
        <h2>Profile Not Found</h2>
        <p>The profile you're looking for doesn't exist.</p>
        <button 
          className="btn"
          onClick={() => window.history.back()}
        >
          Go Back
        </button>
      </div>
    );
  }

  const isOwnProfile = currentUser && currentUser._id === profileUser._id;
  const displayName = getDisplayName(profileUser.profile?.fullname);
  const userInitials = getUserInitials(profileUser.profile?.fullname);
  const joinDate = formatProfileDate(profileUser.createdAt);
  const age = calculateAge(profileUser.profile?.dob);

  return (
    <div className="user-profile">
      {/* Cover Photo and Profile Picture Section */}
      <CoverPhotoSection
        profileUser={profileUser}
        displayName={displayName}
        userInitials={userInitials}
        isOwnProfile={isOwnProfile}
        onThemeToggle={onThemeToggle}
        currentTheme={currentTheme}
      />

      {/* Profile Navigation */}
      <div className="profile-nav">
        <div className="profile-nav-container">
          <div className="profile-tabs">
            <button 
              className={`tab ${activeTab === 'posts' ? 'active' : ''}`}
              onClick={() => setActiveTab('posts')}
            >
              Posts
            </button>
            <button 
              className={`tab ${activeTab === 'about' ? 'active' : ''}`}
              onClick={() => setActiveTab('about')}
            >
              About
            </button>
            <button 
              className={`tab ${activeTab === 'photos' ? 'active' : ''}`}
              onClick={() => setActiveTab('photos')}
            >
              Photos
            </button>
          </div>
        </div>
      </div>

      {/* Profile Content */}
      <div className="profile-content">
        <div className="profile-sidebar">
          <BioSection 
            profileUser={profileUser}
            joinDate={joinDate}
            age={age}
            isOwnProfile={isOwnProfile}
          />
          
          <PhotosSection 
            profileUser={profileUser}
            posts={posts}
            isOwnProfile={isOwnProfile}
          />
        </div>

        <div className="profile-main">
          {activeTab === 'posts' && (
            <div className="posts-section">
              {isOwnProfile && (
                <NewPostForm />
              )}
              <PostsWall 
                posts={posts}
                profileUser={profileUser}
                currentUser={currentUser}
                isOwnProfile={isOwnProfile}
              />
            </div>
          )}

          {activeTab === 'about' && (
            <div className="about-section">
              <div className="card">
                <h3>About {displayName}</h3>
                <div className="about-details">
                  {profileUser.profile?.bio && (
                    <div className="about-item">
                      <strong>Bio:</strong>
                      <p>{profileUser.profile.bio}</p>
                    </div>
                  )}
                  {profileUser.profile?.dob && (
                    <div className="about-item">
                      <strong>Date of Birth:</strong>
                      <p>{profileUser.profile.dob} {age && `(${age} years old)`}</p>
                    </div>
                  )}
                  <div className="about-item">
                    <strong>Joined:</strong>
                    <p>{joinDate}</p>
                  </div>
                  <div className="about-item">
                    <strong>Email:</strong>
                    <p>{profileUser.emails?.[0]?.address}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'photos' && (
            <div className="photos-section">
              <PhotosSection 
                profileUser={profileUser}
                posts={posts}
                isOwnProfile={isOwnProfile}
                expanded={true}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

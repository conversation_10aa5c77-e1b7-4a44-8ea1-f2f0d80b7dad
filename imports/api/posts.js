import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';
import { check } from 'meteor/check';

export const PostsCollection = new Mongo.Collection('posts');

if (Meteor.isServer) {
  // Publications
  Meteor.publish('posts', function() {
    return PostsCollection.find({}, {
      sort: { createdAt: -1 }
    });
  });

  Meteor.publish('userPosts', function(userId) {
    check(userId, String);
    return PostsCollection.find({ userId }, {
      sort: { createdAt: -1 }
    });
  });

  Meteor.publish('singlePost', function(postId) {
    check(postId, String);
    return PostsCollection.find({ _id: postId });
  });
}

// Methods
Meteor.methods({
  'posts.insert'(content, images = []) {
    check(content, String);
    check(images, [String]);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to create posts');
    }

    if (!content.trim()) {
      throw new Meteor.Error('invalid-content', 'Post content cannot be empty');
    }

    const user = Meteor.users.findOne(this.userId);
    if (!user) {
      throw new Meteor.Error('user-not-found', 'User not found');
    }

    return PostsCollection.insert({
      userId: this.userId,
      author: {
        fullname: user.profile?.fullname || user.emails?.[0]?.address,
        email: user.emails?.[0]?.address
      },
      content: content.trim(),
      images: images,
      likes: [],
      comments: [],
      createdAt: new Date(),
      updatedAt: new Date()
    });
  },

  'posts.remove'(postId) {
    check(postId, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to delete posts');
    }

    const post = PostsCollection.findOne(postId);
    if (!post) {
      throw new Meteor.Error('post-not-found', 'Post not found');
    }

    if (post.userId !== this.userId) {
      throw new Meteor.Error('not-authorized', 'You can only delete your own posts');
    }

    return PostsCollection.remove(postId);
  },

  'posts.update'(postId, content, images = []) {
    check(postId, String);
    check(content, String);
    check(images, [String]);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to update posts');
    }

    if (!content.trim()) {
      throw new Meteor.Error('invalid-content', 'Post content cannot be empty');
    }

    const post = PostsCollection.findOne(postId);
    if (!post) {
      throw new Meteor.Error('post-not-found', 'Post not found');
    }

    if (post.userId !== this.userId) {
      throw new Meteor.Error('not-authorized', 'You can only update your own posts');
    }

    return PostsCollection.update(postId, {
      $set: {
        content: content.trim(),
        images: images,
        updatedAt: new Date()
      }
    });
  },

  'posts.like'(postId) {
    check(postId, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to like posts');
    }

    const post = PostsCollection.findOne(postId);
    if (!post) {
      throw new Meteor.Error('post-not-found', 'Post not found');
    }

    const user = Meteor.users.findOne(this.userId);
    const userLike = {
      userId: this.userId,
      fullname: user.profile?.fullname || user.emails?.[0]?.address,
      likedAt: new Date()
    };

    // Check if user already liked this post
    const existingLike = post.likes.find(like => like.userId === this.userId);
    
    if (existingLike) {
      // Unlike the post
      return PostsCollection.update(postId, {
        $pull: { likes: { userId: this.userId } }
      });
    } else {
      // Like the post
      return PostsCollection.update(postId, {
        $push: { likes: userLike }
      });
    }
  },

  'posts.addComment'(postId, content) {
    check(postId, String);
    check(content, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to comment on posts');
    }

    if (!content.trim()) {
      throw new Meteor.Error('invalid-content', 'Comment content cannot be empty');
    }

    const post = PostsCollection.findOne(postId);
    if (!post) {
      throw new Meteor.Error('post-not-found', 'Post not found');
    }

    const user = Meteor.users.findOne(this.userId);
    const comment = {
      _id: new Mongo.ObjectID().toHexString(),
      userId: this.userId,
      author: {
        fullname: user.profile?.fullname || user.emails?.[0]?.address,
        email: user.emails?.[0]?.address
      },
      content: content.trim(),
      createdAt: new Date()
    };

    return PostsCollection.update(postId, {
      $push: { comments: comment }
    });
  },

  'posts.removeComment'(postId, commentId) {
    check(postId, String);
    check(commentId, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to remove comments');
    }

    const post = PostsCollection.findOne(postId);
    if (!post) {
      throw new Meteor.Error('post-not-found', 'Post not found');
    }

    const comment = post.comments.find(c => c._id === commentId);
    if (!comment) {
      throw new Meteor.Error('comment-not-found', 'Comment not found');
    }

    if (comment.userId !== this.userId && post.userId !== this.userId) {
      throw new Meteor.Error('not-authorized', 'You can only remove your own comments or comments on your posts');
    }

    return PostsCollection.update(postId, {
      $pull: { comments: { _id: commentId } }
    });
  }
});

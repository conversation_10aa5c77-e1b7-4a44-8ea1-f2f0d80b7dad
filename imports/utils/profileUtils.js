/**
 * Utility functions for user profile management
 */
import { Meteor } from 'meteor/meteor';

/**
 * Generate a clean URL slug from a user's full name
 * Removes spaces, special characters, and converts to lowercase
 * @param {string} fullname - The user's full name
 * @returns {string} - Clean URL slug
 */
export const generateProfileUrl = (fullname) => {
  if (!fullname || typeof fullname !== 'string') {
    return '';
  }

  return fullname
    .toLowerCase()
    .trim()
    // Replace spaces with empty string
    .replace(/\s+/g, '')
    // Remove special characters, keep only alphanumeric
    .replace(/[^a-z0-9]/g, '')
    // Remove any leading/trailing hyphens
    .replace(/^-+|-+$/g, '');
};

/**
 * Generate a display name from full name
 * @param {string} fullname - The user's full name
 * @returns {string} - Formatted display name
 */
export const getDisplayName = (fullname) => {
  if (!fullname || typeof fullname !== 'string') {
    return 'Unknown User';
  }

  return fullname.trim();
};

/**
 * Get user initials for profile picture placeholder
 * @param {string} fullname - The user's full name
 * @returns {string} - User initials (max 2 characters)
 */
export const getUserInitials = (fullname) => {
  if (!fullname || typeof fullname !== 'string') {
    return 'U';
  }

  const names = fullname.trim().split(' ');
  if (names.length === 1) {
    return names[0].charAt(0).toUpperCase();
  }
  
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
};

/**
 * Find user by profile URL slug (client-side helper)
 * @param {string} profileUrl - The profile URL slug
 * @param {Array} users - Array of users to search through
 * @returns {Object|null} - User object or null if not found
 */
export const findUserByProfileUrl = (profileUrl, users = []) => {
  if (!profileUrl) {
    return null;
  }

  return users.find(user => {
    const userProfileUrl = generateProfileUrl(user.profile?.fullname);
    return userProfileUrl === profileUrl;
  }) || null;
};

/**
 * Check if current user can view a profile
 * @param {string} userId - The user ID of the profile being viewed
 * @param {string} currentUserId - The current user's ID
 * @returns {boolean} - Whether the profile can be viewed
 */
export const canViewProfile = (userId, currentUserId) => {
  // For now, all profiles are public
  // This can be extended later for privacy settings
  return true;
};

/**
 * Format date for profile display
 * @param {Date} date - The date to format
 * @returns {string} - Formatted date string
 */
export const formatProfileDate = (date) => {
  if (!date) {
    return '';
  }

  const options = { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  };
  
  return new Date(date).toLocaleDateString('en-US', options);
};

/**
 * Calculate age from date of birth
 * @param {string} dob - Date of birth in dd.mm.yyyy format
 * @returns {number|null} - Age in years or null if invalid
 */
export const calculateAge = (dob) => {
  if (!dob || typeof dob !== 'string') {
    return null;
  }

  const parts = dob.split('.');
  if (parts.length !== 3) {
    return null;
  }

  const day = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
  const year = parseInt(parts[2], 10);

  if (isNaN(day) || isNaN(month) || isNaN(year)) {
    return null;
  }

  const birthDate = new Date(year, month, day);
  const today = new Date();
  
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age >= 0 ? age : null;
};
